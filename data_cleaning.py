import pandas as pd
import requests
import time
from bs4 import BeautifulSoup
import re
import os

def check_stock_code_exists(stock_code):
    """
    检查股票代码是否在企知道网站上存在
    """
    url = "https://qiye.qizhidao.com/listed-company/home"
    
    # 设置请求头，模拟浏览器访问
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        # 发送GET请求获取页面
        session = requests.Session()
        response = session.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            # 尝试发送搜索请求
            search_url = "https://qiye.qizhidao.com/api/listed-company/search"
            search_data = {
                'keyword': stock_code,
                'page': 1,
                'size': 10
            }
            
            search_response = session.post(search_url, json=search_data, headers=headers, timeout=10)
            
            if search_response.status_code == 200:
                result = search_response.json()
                # 检查是否有搜索结果
                if 'data' in result and result['data'] and len(result['data']) > 0:
                    return True
                else:
                    return False
            else:
                print(f"搜索请求失败，状态码: {search_response.status_code}")
                return False
                
    except Exception as e:
        print(f"检查股票代码 {stock_code} 时出错: {str(e)}")
        return False
    
    return False

def format_stock_code(code):
    """
    将股票代码格式化为6位数字
    """
    if pd.isna(code):
        return None
    
    # 转换为字符串并去除空格
    code_str = str(code).strip()
    
    # 如果包含字母，提取数字部分
    numbers = re.findall(r'\d+', code_str)
    if numbers:
        code_str = numbers[0]
    
    # 转换为整数再转回字符串，去除前导零
    try:
        code_int = int(float(code_str))
        # 格式化为6位数字，不足6位前面补0
        formatted_code = f"{code_int:06d}"
        return formatted_code
    except:
        return None

def clean_data():
    """
    清洗Excel数据
    """
    # 读取Excel文件
    file_path = "csv_data/2000-2023已剔除已缩尾.xlsx"
    
    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(file_path)
        print(f"原始数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 显示前几行数据以了解结构
        print("\n前5行数据:")
        print(df.head())
        
        # 检查是否有年份列
        year_columns = [col for col in df.columns if '年' in str(col) or 'year' in str(col).lower() or '时间' in str(col)]
        print(f"\n可能的年份列: {year_columns}")
        
        # 检查是否有证券代码列
        code_columns = [col for col in df.columns if '代码' in str(col) or 'code' in str(col).lower() or '证券' in str(col)]
        print(f"可能的证券代码列: {code_columns}")
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错: {str(e)}")
        return None

def main():
    """
    主函数
    """
    print("开始数据清洗...")
    
    # 首先读取并检查数据结构
    df = clean_data()
    
    if df is not None:
        print("\n请检查上述输出，确认年份列和证券代码列的名称")
        print("然后我们将继续进行数据清洗...")
        
        # 这里暂停，让用户确认列名
        input("按Enter键继续...")
        
        # 根据实际列名进行后续处理
        # 这部分代码需要根据实际的列名进行调整
        
if __name__ == "__main__":
    main()
