import pandas as pd
import time
import re

# 注意：网站验证功能已简化，如需完整验证请安装selenium并取消注释相关代码
# def check_stock_code_exists(stock_code):
#     """
#     检查股票代码是否在企知道网站上存在
#     需要安装: pip install selenium
#     需要下载ChromeDriver
#     """
#     pass

def format_stock_code(code):
    """
    将股票代码格式化为6位数字
    例如: 2 -> 000002, 123 -> 000123, 123456 -> 123456
    """
    if pd.isna(code):
        return None

    # 转换为字符串并去除空格
    code_str = str(code).strip()

    # 如果包含小数点，去除小数部分
    if '.' in code_str:
        code_str = code_str.split('.')[0]

    # 如果包含字母，提取数字部分
    numbers = re.findall(r'\d+', code_str)
    if numbers:
        code_str = numbers[0]

    # 转换为整数再转回字符串，去除前导零然后重新格式化
    try:
        code_int = int(code_str)
        # 格式化为6位数字，不足6位前面补0
        formatted_code = f"{code_int:06d}"
        return formatted_code
    except:
        return None

def simple_check_stock_code(stock_code):
    """
    简化的股票代码检查函数
    由于网站验证比较复杂，这里先实现基本的格式检查
    """
    if not stock_code or len(stock_code) != 6:
        return False

    # 检查是否为6位数字
    if not stock_code.isdigit():
        return False

    # 基本的股票代码范围检查
    code_int = int(stock_code)

    # 上交所: 600000-699999, 000000-099999 (部分)
    # 深交所: 000000-399999
    # 创业板: 300000-399999
    if (600000 <= code_int <= 699999) or (000000 <= code_int <= 399999):
        return True

    return False

def clean_data():
    """
    清洗Excel数据：筛选2014-2023年数据，格式化股票代码，验证代码有效性
    """
    # 读取Excel文件
    file_path = "csv_data/2000-2023已剔除已缩尾.xlsx"

    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(file_path)
        print(f"原始数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")

        # 显示前几行数据以了解结构
        print("\n前5行数据:")
        print(df.head())

        # 自动识别年份列和证券代码列
        year_columns = [col for col in df.columns if '年' in str(col) or 'year' in str(col).lower() or '时间' in str(col) or 'date' in str(col).lower()]
        code_columns = [col for col in df.columns if '代码' in str(col) or 'code' in str(col).lower() or '证券' in str(col)]

        print(f"\n可能的年份列: {year_columns}")
        print(f"可能的证券代码列: {code_columns}")

        # 如果没有找到明确的列名，让用户手动指定
        if not year_columns:
            print("\n未找到年份列，请手动指定年份列名:")
            for i, col in enumerate(df.columns):
                print(f"{i}: {col}")
            year_col_idx = int(input("请输入年份列的索引: "))
            year_col = df.columns[year_col_idx]
        else:
            year_col = year_columns[0]

        if not code_columns:
            print("\n未找到证券代码列，请手动指定证券代码列名:")
            for i, col in enumerate(df.columns):
                print(f"{i}: {col}")
            code_col_idx = int(input("请输入证券代码列的索引: "))
            code_col = df.columns[code_col_idx]
        else:
            code_col = code_columns[0]

        print(f"\n使用年份列: {year_col}")
        print(f"使用证券代码列: {code_col}")

        # 1. 筛选2014-2023年的数据
        print("\n正在筛选2014-2023年的数据...")
        df_filtered = df[(df[year_col] >= 2014) & (df[year_col] <= 2023)].copy()
        print(f"筛选后数据形状: {df_filtered.shape}")

        # 2. 格式化证券代码为6位数
        print("\n正在格式化证券代码...")
        df_filtered['formatted_code'] = df_filtered[code_col].apply(format_stock_code)

        # 显示格式化前后的对比
        print("\n证券代码格式化对比（前10个）:")
        comparison = df_filtered[[code_col, 'formatted_code']].head(10)
        print(comparison)

        # 3. 移除无法格式化的代码
        before_count = len(df_filtered)
        df_filtered = df_filtered.dropna(subset=['formatted_code'])
        after_count = len(df_filtered)
        print(f"\n移除无法格式化的代码: {before_count - after_count} 条记录")

        # 4. 简单验证股票代码（基于格式）
        print("\n正在验证股票代码格式...")
        df_filtered['code_valid'] = df_filtered['formatted_code'].apply(simple_check_stock_code)

        valid_count = df_filtered['code_valid'].sum()
        total_count = len(df_filtered)
        print(f"有效股票代码: {valid_count}/{total_count}")

        # 5. 只保留有效的股票代码
        df_final = df_filtered[df_filtered['code_valid']].copy()
        df_final = df_final.drop(['code_valid'], axis=1)

        # 6. 更新原始的证券代码列
        df_final[code_col] = df_final['formatted_code']
        df_final = df_final.drop(['formatted_code'], axis=1)

        print(f"\n最终清洗后数据形状: {df_final.shape}")

        # 保存清洗后的数据
        output_path = "csv_data/cleaned_data_2014_2023.xlsx"
        df_final.to_excel(output_path, index=False)
        print(f"\n清洗后的数据已保存到: {output_path}")

        return df_final

    except Exception as e:
        print(f"处理数据时出错: {str(e)}")
        return None

def main():
    """
    主函数
    """
    print("=" * 60)
    print("数据清洗程序")
    print("功能: 筛选2014-2023年数据，格式化股票代码为6位数")
    print("=" * 60)

    # 执行数据清洗
    df_cleaned = clean_data()

    if df_cleaned is not None:
        print("\n" + "=" * 60)
        print("数据清洗完成!")
        print(f"最终数据包含 {len(df_cleaned)} 条记录")
        print("清洗后的数据已保存到: csv_data/cleaned_data_2014_2023.xlsx")

        # 显示一些统计信息
        if '年份' in df_cleaned.columns or any('年' in str(col) for col in df_cleaned.columns):
            year_col = next((col for col in df_cleaned.columns if '年' in str(col)), None)
            if year_col:
                print(f"\n各年份数据分布:")
                year_counts = df_cleaned[year_col].value_counts().sort_index()
                for year, count in year_counts.items():
                    print(f"  {year}年: {count} 条记录")

        # 显示股票代码样例
        code_col = next((col for col in df_cleaned.columns if '代码' in str(col)), None)
        if code_col:
            print(f"\n股票代码样例 (已格式化为6位数):")
            sample_codes = df_cleaned[code_col].unique()[:10]
            for code in sample_codes:
                print(f"  {code}")

        print("\n" + "=" * 60)
    else:
        print("数据清洗失败!")

if __name__ == "__main__":
    main()
